name: 'Determine S3 Bucket Name'
description: 'Determines S3 bucket name based on environment and region'
inputs:
  environment:
    description: 'Environment (non-prod or production)'
    required: true
  region:
    description: 'AWS region'
    required: true
  bucket-type:
    description: 'Type of bucket (packages, legacy-deployer, etc.)'
    required: true
    default: 'packages'
  service-name:
    description: 'Service name for bucket naming'
    required: false
    default: 'ais.1-0.application'
outputs:
  bucket-name:
    description: 'S3 bucket name'
    value: ${{ steps.determine-bucket.outputs.bucket-name }}
  region-abbreviation:
    description: 'Region abbreviation'
    value: ${{ steps.region-abbr.outputs.region-abbreviation }}

runs:
  using: 'composite'
  steps:
    - name: Get region abbreviation
      id: region-abbr
      shell: bash
      run: |
        REGION="${{ inputs.region }}"
        # Extract parts of the region (e.g., us-east-1 => us, east, 1)
        IFS='-' read -ra PARTS <<< "$REGION"
        # Create abbreviation (e.g., us-east-1 => ue1)
        ABBR="${PARTS[0]:0:1}${PARTS[1]:0:1}${PARTS[2]}"
        echo "region-abbreviation=$ABBR" >> $GITHUB_OUTPUT
        echo "Region abbreviation: $ABBR"

    - name: Determine bucket name
      id: determine-bucket
      shell: bash
      run: |
        REGION_ABBR="${{ steps.region-abbr.outputs.region-abbreviation }}"
        
        # Check if production environment
        IS_PROD=${{ startsWith(inputs.environment, 'prod') && 'true' || 'false' }}
        
        # Build bucket name based on type and environment
        case "${{ inputs.bucket-type }}" in
          "packages")
            if [ "$IS_PROD" = "true" ]; then
              BUCKET_NAME="${{ inputs.service-name }}.packages.$REGION_ABBR"
            else
              BUCKET_NAME="${{ inputs.service-name }}.packages.np.$REGION_ABBR"
            fi
            ;;
          "legacy-deployer")
            BUCKET_NAME="ais.authoring.legacy-deployer.packages"
            ;;
          *)
            echo "Unknown bucket type: ${{ inputs.bucket-type }}"
            exit 1
            ;;
        esac
        
        echo "bucket-name=$BUCKET_NAME" >> $GITHUB_OUTPUT
        echo "Using S3 bucket: $BUCKET_NAME"
