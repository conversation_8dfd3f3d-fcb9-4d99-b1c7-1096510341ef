name: 'Load Workflow Configuration'
description: 'Loads workflow configuration from defaults file and sets environment variables'
inputs:
  config-file:
    description: 'Path to configuration file'
    required: false
    default: '.github/workflows/config/workflow-defaults.yml'
outputs:
  aws-default-account:
    description: 'Default AWS account'
    value: ${{ steps.load-config.outputs.aws-default-account }}
  aws-default-region:
    description: 'Default AWS region'
    value: ${{ steps.load-config.outputs.aws-default-region }}
  terraform-version:
    description: 'Terraform version'
    value: ${{ steps.load-config.outputs.terraform-version }}
  node-version:
    description: 'Node.js version'
    value: ${{ steps.load-config.outputs.node-version }}
  default-environment:
    description: 'Default environment'
    value: ${{ steps.load-config.outputs.default-environment }}

runs:
  using: 'composite'
  steps:
    - name: Checkout code (if not already checked out)
      uses: actions/checkout@v4
      if: ${{ !env.GITHUB_WORKSPACE }}

    - name: Install yq for YAML parsing
      shell: bash
      run: |
        if ! command -v yq &> /dev/null; then
          sudo wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64
          sudo chmod +x /usr/local/bin/yq
        fi

    - name: Load configuration
      id: load-config
      shell: bash
      run: |
        CONFIG_FILE="${{ inputs.config-file }}"
        
        if [ ! -f "$CONFIG_FILE" ]; then
          echo "Configuration file not found: $CONFIG_FILE"
          echo "Using fallback defaults"
          
          # Fallback defaults
          echo "aws-default-account=awsaaianp" >> $GITHUB_OUTPUT
          echo "aws-default-region=us-east-1" >> $GITHUB_OUTPUT
          echo "terraform-version=1.0.11" >> $GITHUB_OUTPUT
          echo "node-version=14" >> $GITHUB_OUTPUT
          echo "default-environment=non-prod" >> $GITHUB_OUTPUT
        else
          echo "Loading configuration from: $CONFIG_FILE"
          
          # Parse YAML and extract values
          AWS_DEFAULT_ACCOUNT=$(yq eval '.aws.default_account' "$CONFIG_FILE")
          AWS_DEFAULT_REGION=$(yq eval '.aws.default_region' "$CONFIG_FILE")
          TERRAFORM_VERSION=$(yq eval '.tool_versions.terraform' "$CONFIG_FILE")
          NODE_VERSION=$(yq eval '.tool_versions.node' "$CONFIG_FILE")
          DEFAULT_ENVIRONMENT=$(yq eval '.environments.default' "$CONFIG_FILE")
          
          # Set outputs
          echo "aws-default-account=$AWS_DEFAULT_ACCOUNT" >> $GITHUB_OUTPUT
          echo "aws-default-region=$AWS_DEFAULT_REGION" >> $GITHUB_OUTPUT
          echo "terraform-version=$TERRAFORM_VERSION" >> $GITHUB_OUTPUT
          echo "node-version=$NODE_VERSION" >> $GITHUB_OUTPUT
          echo "default-environment=$DEFAULT_ENVIRONMENT" >> $GITHUB_OUTPUT
          
          # Set environment variables for use in subsequent steps
          echo "AWS_DEFAULT_ACCOUNT=$AWS_DEFAULT_ACCOUNT" >> $GITHUB_ENV
          echo "AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION" >> $GITHUB_ENV
          echo "TERRAFORM_VERSION=$TERRAFORM_VERSION" >> $GITHUB_ENV
          echo "NODE_VERSION=$NODE_VERSION" >> $GITHUB_ENV
          echo "DEFAULT_ENVIRONMENT=$DEFAULT_ENVIRONMENT" >> $GITHUB_ENV
          
          echo "Configuration loaded successfully"
        fi
