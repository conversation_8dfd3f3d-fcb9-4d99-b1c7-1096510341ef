name: 'Multi-Region Docker Push'
description: 'Pushes <PERSON><PERSON> image to additional AWS regions for production'
inputs:
  primary-region:
    description: 'Primary AWS region'
    required: true
  environment:
    description: 'Environment (non-prod or production)'
    required: true
  repository-name:
    description: 'ECR repository name'
    required: true
  aws-account-id:
    description: 'AWS account ID'
    required: true
  primary-registry:
    description: 'Primary ECR registry URL'
    required: true
  image-tag:
    description: 'Docker image tag'
    required: false
    default: 'latest'
outputs:
  additional-regions:
    description: 'Additional regions where image was pushed'
    value: ${{ steps.determine-regions.outputs.additional-regions }}

runs:
  using: 'composite'
  steps:
    - name: Determine additional regions
      id: determine-regions
      shell: bash
      run: |
        ADDITIONAL_REGIONS=""
        
        # Only push to additional regions for production
        IS_PROD=${{ startsWith(inputs.environment, 'prod') && 'true' || 'false' }}
        
        if [ "$IS_PROD" = "true" ]; then
          # For production, push to both us-east-1 and us-west-2
          if [ "${{ inputs.primary-region }}" = "us-east-1" ]; then
            ADDITIONAL_REGIONS="us-west-2"
          elif [ "${{ inputs.primary-region }}" = "us-west-2" ]; then
            ADDITIONAL_REGIONS="us-east-1"
          fi
        fi
        
        echo "additional-regions=$ADDITIONAL_REGIONS" >> $GITHUB_OUTPUT
        echo "Additional regions: $ADDITIONAL_REGIONS"

    - name: Push to additional regions
      if: steps.determine-regions.outputs.additional-regions != ''
      shell: bash
      run: |
        ADDITIONAL_REGION="${{ steps.determine-regions.outputs.additional-regions }}"
        
        # Configure AWS credentials for the additional region
        aws configure set region $ADDITIONAL_REGION
        
        # Login to ECR in the additional region
        aws ecr get-login-password | docker login --username AWS --password-stdin ${{ inputs.aws-account-id }}.dkr.ecr.$ADDITIONAL_REGION.amazonaws.com
        
        # Create repository if it doesn't exist
        aws ecr describe-repositories --repository-names ${{ inputs.repository-name }}_${{ inputs.environment }} || \
        aws ecr create-repository --repository-name ${{ inputs.repository-name }}_${{ inputs.environment }}
        
        # Tag and push the image
        SOURCE_IMAGE="${{ inputs.primary-registry }}/${{ inputs.repository-name }}_${{ inputs.environment }}:${{ inputs.image-tag }}"
        TARGET_IMAGE="${{ inputs.aws-account-id }}.dkr.ecr.$ADDITIONAL_REGION.amazonaws.com/${{ inputs.repository-name }}_${{ inputs.environment }}:${{ inputs.image-tag }}"
        
        docker tag $SOURCE_IMAGE $TARGET_IMAGE
        docker push $TARGET_IMAGE
        
        echo "Successfully pushed image to additional region: $ADDITIONAL_REGION"
