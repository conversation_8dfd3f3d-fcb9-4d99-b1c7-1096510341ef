name: 'Prepare Docker Context'
description: 'Prepares <PERSON><PERSON> build context with codebase package'
inputs:
  artifact-name:
    description: 'Name of the codebase artifact to download'
    required: true
    default: 'codebase-package'
  docker-context-path:
    description: 'Path where Docker context should be prepared'
    required: true
  package-filename:
    description: 'Name of the package file'
    required: false
    default: 'codebasePackage.zip'
outputs:
  context-path:
    description: 'Path to the prepared Docker context'
    value: ${{ inputs.docker-context-path }}

runs:
  using: 'composite'
  steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download codebase package
      uses: actions/download-artifact@v3
      with:
        name: ${{ inputs.artifact-name }}
        path: artifacts

    - name: Prepare Docker context directory
      shell: bash
      run: |
        mkdir -p ${{ inputs.docker-context-path }}
        echo "Created Docker context directory: ${{ inputs.docker-context-path }}"

    - name: Copy codebase package to Docker context
      shell: bash
      run: |
        cp artifacts/${{ inputs.package-filename }} ${{ inputs.docker-context-path }}/
        echo "Copied ${{ inputs.package-filename }} to Docker context"
        
        # List contents for verification
        echo "Docker context contents:"
        ls -la ${{ inputs.docker-context-path }}/

    - name: Upload Docker context
      uses: actions/upload-artifact@v3
      with:
        name: docker-context
        path: ${{ inputs.docker-context-path }}/
