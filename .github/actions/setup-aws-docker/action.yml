name: 'Setup AWS and Docker'
description: 'Configures AWS credentials and logs into Amazon ECR'
inputs:
  aws-account:
    description: 'AWS account ID or name'
    required: true
  aws-region:
    description: 'AWS region'
    required: true
    default: 'us-east-1'
  role-name:
    description: 'IAM role name for GitHub Actions'
    required: false
    default: 'github-actions-role'
outputs:
  registry:
    description: 'ECR registry URL'
    value: ${{ steps.login-ecr.outputs.registry }}
  aws-account-id:
    description: 'AWS account ID'
    value: ${{ steps.get-account.outputs.aws-account-id }}

runs:
  using: 'composite'
  steps:
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: arn:aws:iam::${{ inputs.aws-account }}:role/${{ inputs.role-name }}
        aws-region: ${{ inputs.aws-region }}

    - name: Get AWS account ID
      id: get-account
      shell: bash
      run: |
        ACCOUNT_ID=$(aws sts get-caller-identity --query "Account" --output text)
        echo "aws-account-id=$ACCOUNT_ID" >> $GITHUB_OUTPUT
        echo "AWS Account ID: $ACCOUNT_ID"

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
