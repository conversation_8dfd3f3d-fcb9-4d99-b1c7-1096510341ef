name: 'Validate Workflow Inputs'
description: 'Validates common workflow inputs and sets environment variables'
inputs:
  environment:
    description: 'Environment (non-prod or production)'
    required: true
  region:
    description: 'AWS region'
    required: true
  account:
    description: 'AWS account'
    required: true
  allowed-regions:
    description: 'Comma-separated list of allowed regions'
    required: false
    default: 'us-east-1,us-west-2'
  allowed-environments:
    description: 'Comma-separated list of allowed environments'
    required: false
    default: 'non-prod,production'
outputs:
  is-production:
    description: 'Whether this is a production environment'
    value: ${{ steps.validate.outputs.is-production }}
  region-valid:
    description: 'Whether the region is valid'
    value: ${{ steps.validate.outputs.region-valid }}
  environment-valid:
    description: 'Whether the environment is valid'
    value: ${{ steps.validate.outputs.environment-valid }}

runs:
  using: 'composite'
  steps:
    - name: Validate inputs
      id: validate
      shell: bash
      run: |
        # Validate environment
        ALLOWED_ENVS="${{ inputs.allowed-environments }}"
        if [[ ",$ALLOWED_ENVS," == *",${{ inputs.environment }},"* ]]; then
          echo "environment-valid=true" >> $GITHUB_OUTPUT
          echo "✅ Environment '${{ inputs.environment }}' is valid"
        else
          echo "environment-valid=false" >> $GITHUB_OUTPUT
          echo "❌ Environment '${{ inputs.environment }}' is not in allowed list: $ALLOWED_ENVS"
          exit 1
        fi
        
        # Validate region
        ALLOWED_REGIONS="${{ inputs.allowed-regions }}"
        if [[ ",$ALLOWED_REGIONS," == *",${{ inputs.region }},"* ]]; then
          echo "region-valid=true" >> $GITHUB_OUTPUT
          echo "✅ Region '${{ inputs.region }}' is valid"
        else
          echo "region-valid=false" >> $GITHUB_OUTPUT
          echo "❌ Region '${{ inputs.region }}' is not in allowed list: $ALLOWED_REGIONS"
          exit 1
        fi
        
        # Check if production
        IS_PROD=${{ startsWith(inputs.environment, 'prod') && 'true' || 'false' }}
        echo "is-production=$IS_PROD" >> $GITHUB_OUTPUT
        echo "Production environment: $IS_PROD"
        
        # Set common environment variables
        echo "ENVIRONMENT=${{ inputs.environment }}" >> $GITHUB_ENV
        echo "REGION=${{ inputs.region }}" >> $GITHUB_ENV
        echo "ACCOUNT=${{ inputs.account }}" >> $GITHUB_ENV
        echo "IS_PRODUCTION=$IS_PROD" >> $GITHUB_ENV
        echo "BUILD_NUMBER=$(date +%Y%m%d%H%M%S)" >> $GITHUB_ENV
        echo "USERNAME=${{ github.actor }}" >> $GITHUB_ENV
        echo "LAUNCHED_ON=$(date -u +"%Y-%m-%dT%H:%M:%SZ")" >> $GITHUB_ENV
