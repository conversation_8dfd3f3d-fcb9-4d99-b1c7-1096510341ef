# GitHub Actions Workflows

This directory contains GitHub Actions workflows that replace the original Cake build scripts.

## Main Workflows

### Codebase Deployment
- `deploy-codebase.yml` - Deploys the codebase to AWS S3 buckets
- `deploy-legacy-packages.yml` - Packages and deploys legacy components to S3

### Docker Image Workflows
- `deploy-processing-app-image.yml` - Builds and pushes Docker images for processing applications
- `deploy-web-app-image.yml` - Builds and pushes Docker images for web applications
- `deploy-base-images.yml` - Builds and pushes base Docker images
- `pull-docker-image.yml` - Pulls Docker images from ECR

### Infrastructure Workflows
- `deploy-terraform-component.yml` - Deploys infrastructure using Terraform
- `deploy-ami.yml` - Creates AWS AMIs using Terraform
- `deploy-ami-webstacks.yml` - Deploys AMIs specifically for webstacks

### Utility Workflows
- `npm-install.yml` - Runs npm install in lambda source directories

### Security Workflows
- `scheduled-security-scan.yml` - Runs scheduled security scans for code, dependencies, Docker images, and Terraform

## Reusable Workflow Components

### AWS and Setup
- `reusable-aws-auth.yml` - Handles AWS authentication and common setup tasks (Terraform, Docker, Node.js)

### Build Operations
- `reusable-build-steps.yml` - Handles common build operations (packaging, zipping, npm install)
- `reusable-docker-operations.yml` - Provides reusable jobs for Docker operations (push, pull)

### Infrastructure Operations
- `reusable-terraform-operations.yml` - Handles Terraform operations (plan, apply, destroy)

### Security Operations
- `reusable-security-scan.yml` - Handles security scanning operations (code, dependencies, Docker)

## Composite Actions

The following composite actions are available in `.github/actions/` for common step sequences:

### AWS Operations
- `setup-aws-docker/` - Configures AWS credentials and logs into ECR
- `determine-s3-bucket/` - Determines S3 bucket names based on environment and region
- `multi-region-docker-push/` - Handles Docker image pushing to multiple regions

### Docker Operations
- `prepare-docker-context/` - Prepares Docker build context with codebase package
- `docker-build-push/` - Builds and pushes Docker images with standardized tagging

### Utility Actions
- `validate-inputs/` - Validates common workflow inputs and sets defaults
- `setup-build-environment/` - Sets up common build tools and environment variables

## Usage

### Manual Triggering
Each workflow can be triggered manually from the GitHub Actions tab with the appropriate inputs.

### Security Scanning
Security scanning is integrated into the deployment workflows and can be enabled with the `run-security-scan` input parameter. Additionally, the `scheduled-security-scan.yml` workflow runs automatically every Monday at 2:00 AM UTC.

### Reusable Components
The reusable workflow components can be used in custom workflows by referencing them with the `uses` keyword. For example:

```yaml
jobs:
  aws-auth:
    uses: ./.github/workflows/reusable-aws-auth.yml
    with:
      aws-region: 'us-east-1'
      environment: 'non-prod'
```
