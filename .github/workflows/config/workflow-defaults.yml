# Workflow Configuration Defaults
# This file contains default values and configuration for GitHub Actions workflows
# to reduce hardcoded values and improve maintainability

# AWS Configuration
aws:
  default_account: "awsaaianp"
  default_region: "us-east-1"
  allowed_regions:
    - "us-east-1"
    - "us-west-2"
  role_name: "github-actions-role"

# Environment Configuration
environments:
  allowed:
    - "non-prod"
    - "production"
  default: "non-prod"

# Tool Versions
tool_versions:
  terraform: "1.0.11"
  node: "14"
  java: "17"

# Docker Configuration
docker:
  default_tag: "latest"
  build_platforms:
    - "linux/amd64"
  
# ECR Repository Defaults
ecr:
  repositories:
    webapp: "webapp"
    processing: "processingapps"
    base_images: "base-images"

# S3 Bucket Configuration
s3:
  bucket_types:
    packages:
      service_name: "ais.1-0.application"
      suffix_prod: "packages"
      suffix_nonprod: "packages.np"
    legacy_deployer:
      name: "ais.authoring.legacy-deployer.packages"

# Security Scan Configuration
security:
  default_severity: "high"
  fail_on_severity: "critical"
  scan_types:
    - "code"
    - "dependencies"
    - "docker"
    - "terraform"

# Artifact Configuration
artifacts:
  default_retention_days: 30
  codebase_package_name: "codebasePackage.zip"
  exclude_patterns:
    - ".git/*"
    - ".github/*"
    - "artifacts/*"
    - "*.git*"

# Notification Configuration
notifications:
  enabled: true
  channels:
    - "github-summary"
    # Add other notification channels as needed
