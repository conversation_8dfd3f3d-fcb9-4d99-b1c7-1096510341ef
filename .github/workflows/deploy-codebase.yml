name: Deploy Codebase

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'non-prod'
        type: choice
        options:
          - non-prod
          - production
      region:
        description: 'AWS region'
        required: true
        default: 'us-east-1'
        type: choice
        options:
          - us-east-1
          - us-west-2
      account:
        description: 'AWS account'
        required: true
        default: 'awsaaianp'
        type: string
      package-name:
        description: 'Package name'
        required: true
        default: 'codebasePackage.zip'
        type: string
      run-security-scan:
        description: 'Run security scan'
        required: false
        default: false
        type: boolean

jobs:
  # Security scanning job (optional)
  security-scan:
    if: github.event.inputs.run-security-scan == 'true'
    uses: ./.github/workflows/reusable-security-scan.yml
    with:
      scan-type: 'code'
      upload-sarif: true
      fail-on-severity: 'high'

  # AWS authentication and setup
  aws-auth:
    uses: ./.github/workflows/reusable-aws-auth.yml
    with:
      aws-region: ${{ github.event.inputs.region }}
      environment: ${{ github.event.inputs.environment }}
      account: ${{ github.event.inputs.account }}

  # Package the codebase
  package-codebase:
    name: Package Codebase
    needs: aws-auth
    uses: ./.github/workflows/reusable-build-steps.yml
    with:
      operation: 'zip'
      output-path: 'artifacts/${{ github.event.inputs.package-name }}'
      exclude-patterns: '.git/*,.github/*,artifacts/*'
      artifact-name: 'codebase-package'

  # Deploy the package to S3
  deploy-codebase:
    name: Deploy Codebase
    needs: [aws-auth, package-codebase]
    runs-on: ubuntu-latest

    steps:
      - name: Validate inputs
        uses: ./.github/actions/validate-inputs
        with:
          environment: ${{ github.event.inputs.environment }}
          region: ${{ github.event.inputs.region }}
          account: ${{ github.event.inputs.account }}

      - name: Download codebase package
        uses: actions/download-artifact@v3
        with:
          name: codebase-package
          path: artifacts

      - name: Setup AWS credentials
        uses: ./.github/actions/setup-aws-docker
        with:
          aws-account: ${{ github.event.inputs.account }}
          aws-region: ${{ github.event.inputs.region }}

      - name: Determine S3 bucket
        id: s3-bucket
        uses: ./.github/actions/determine-s3-bucket
        with:
          environment: ${{ github.event.inputs.environment }}
          region: ${{ github.event.inputs.region }}
          bucket-type: 'packages'

      - name: Upload package to S3
        run: |
          # Upload the package to S3
          aws s3 cp artifacts/${{ github.event.inputs.package-name }} s3://${{ steps.s3-bucket.outputs.bucket-name }}/${{ github.event.inputs.environment }}/

          echo "Uploaded package to S3: s3://${{ steps.s3-bucket.outputs.bucket-name }}/${{ github.event.inputs.environment }}/${{ github.event.inputs.package-name }}"

      - name: Run SSM commands on EC2 instances (if applicable)
        if: false  # Disabled for now, enable when SSM integration is needed
        run: |
          # Get EC2 instances in the target environment
          INSTANCE_IDS=$(aws ec2 describe-instances --filters "Name=tag:Environment,Values=${{ github.event.inputs.environment }}" --query "Reservations[].Instances[].InstanceId" --output text)

          # Run SSM command on each instance
          for INSTANCE_ID in $INSTANCE_IDS; do
            aws ssm send-command \
              --instance-ids "$INSTANCE_ID" \
              --document-name "AWS-RunShellScript" \
              --parameters "commands=['echo \"Deploying codebase package\"', 'aws s3 cp s3://${{ steps.s3-bucket.outputs.bucket-name }}/${{ github.event.inputs.environment }}/${{ github.event.inputs.package-name }} /tmp/', 'unzip -o /tmp/${{ github.event.inputs.package-name }} -d /data/git/AIS-1.0/']" \
              --comment "Deploy codebase package from GitHub Actions"
          done
