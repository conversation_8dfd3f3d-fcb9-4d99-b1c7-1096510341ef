name: Deploy Legacy Packages

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'non-prod'
        type: choice
        options:
          - non-prod
          - production
      account:
        description: 'AWS account'
        required: true
        default: 'awsaaianp'
        type: string
      branch_name:
        description: 'Branch name for package organization (optional)'
        required: false
        type: string
        default: ''
      run-security-scan:
        description: 'Run security scan'
        required: false
        default: false
        type: boolean

jobs:
  # Security scanning job (optional)
  security-scan:
    if: github.event.inputs.run-security-scan == 'true'
    uses: ./.github/workflows/reusable-security-scan.yml
    with:
      scan-type: 'code'
      upload-sarif: true
      fail-on-severity: 'high'

  # AWS authentication and setup
  aws-auth:
    uses: ./.github/workflows/reusable-aws-auth.yml
    with:
      aws-region: 'us-east-1'
      environment: ${{ github.event.inputs.environment }}
      account: ${{ github.event.inputs.account }}

  # Package common_libs
  package-common-libs:
    name: Package Common Libraries
    needs: aws-auth
    uses: ./.github/workflows/reusable-build-steps.yml
    with:
      operation: 'package'
      source-path: 'common_libs'
      output-path: 'artifacts/legacy-deployer/legacy-common-libraries.zip'
      exclude-patterns: '*.git*'
      artifact-name: 'legacy-common-libraries'

  # Package NewSystem
  package-new-system:
    name: Package NewSystem
    needs: aws-auth
    uses: ./.github/workflows/reusable-build-steps.yml
    with:
      operation: 'package'
      source-path: 'NewSystem'
      output-path: 'artifacts/legacy-deployer/legacy-authoring.zip'
      exclude-patterns: '*.git*'
      artifact-name: 'legacy-authoring'

  # Package ResidualsEntry
  package-residuals-entry:
    name: Package ResidualsEntry
    needs: aws-auth
    uses: ./.github/workflows/reusable-build-steps.yml
    with:
      operation: 'package'
      source-path: 'ResidualsEntry'
      output-path: 'artifacts/legacy-deployer/legacy-residuals-entry.zip'
      exclude-patterns: '*.git*'
      artifact-name: 'legacy-residuals-entry'

  # Package Auditing
  package-auditing:
    name: Package Auditing
    needs: aws-auth
    uses: ./.github/workflows/reusable-build-steps.yml
    with:
      operation: 'package'
      source-path: 'Auditing'
      output-path: 'artifacts/legacy-deployer/legacy-auditing.zip'
      exclude-patterns: '*.git*'
      artifact-name: 'legacy-auditing'

  # Package DigIRS
  package-digirs:
    name: Package DigIRS
    needs: aws-auth
    uses: ./.github/workflows/reusable-build-steps.yml
    with:
      operation: 'package'
      source-path: 'DigIRS'
      output-path: 'artifacts/legacy-deployer/legacy-digirs.zip'
      exclude-patterns: '*.git*'
      artifact-name: 'legacy-digirs'

  # Package AdvertisedPrograms
  package-advertised-programs:
    name: Package AdvertisedPrograms
    needs: aws-auth
    uses: ./.github/workflows/reusable-build-steps.yml
    with:
      operation: 'package'
      source-path: 'AdvertisedPrograms'
      output-path: 'artifacts/legacy-deployer/legacy-advertised-programs.zip'
      exclude-patterns: '*.git*'
      artifact-name: 'legacy-advertised-programs'

  # Package AdvPgmDelivery
  package-adv-pgm-delivery:
    name: Package AdvPgmDelivery
    needs: aws-auth
    uses: ./.github/workflows/reusable-build-steps.yml
    with:
      operation: 'package'
      source-path: 'AdvPgmDelivery'
      output-path: 'artifacts/legacy-deployer/legacy-advertised-program-delivery.zip'
      exclude-patterns: '*.git*'
      artifact-name: 'legacy-advertised-program-delivery'

  # Deploy packages to S3
  deploy-packages:
    name: Deploy Packages to S3
    needs: [
      aws-auth,
      package-common-libs,
      package-new-system,
      package-residuals-entry,
      package-auditing,
      package-digirs,
      package-advertised-programs,
      package-adv-pgm-delivery
    ]
    runs-on: ubuntu-latest
    env:
      BRANCH_NAME: ${{ github.event.inputs.branch_name }}

    steps:
      - name: Validate inputs
        uses: ./.github/actions/validate-inputs
        with:
          environment: ${{ github.event.inputs.environment }}
          region: 'us-east-1'
          account: ${{ github.event.inputs.account }}

      - name: Setup AWS credentials
        uses: ./.github/actions/setup-aws-docker
        with:
          aws-account: ${{ github.event.inputs.account }}
          aws-region: 'us-east-1'

      - name: Determine S3 bucket
        id: s3-bucket
        uses: ./.github/actions/determine-s3-bucket
        with:
          environment: ${{ github.event.inputs.environment }}
          region: 'us-east-1'
          bucket-type: 'legacy-deployer'

      - name: Create artifacts directory
        run: mkdir -p artifacts/legacy-deployer

      - name: Download all artifacts
        uses: actions/download-artifact@v3
        with:
          path: artifacts/legacy-deployer

      - name: Prepare packages for upload
        run: |
          # Move files from artifact directories to the main directory
          mv artifacts/legacy-deployer/legacy-common-libraries/legacy-common-libraries.zip artifacts/legacy-deployer/
          mv artifacts/legacy-deployer/legacy-authoring/legacy-authoring.zip artifacts/legacy-deployer/
          mv artifacts/legacy-deployer/legacy-residuals-entry/legacy-residuals-entry.zip artifacts/legacy-deployer/
          mv artifacts/legacy-deployer/legacy-auditing/legacy-auditing.zip artifacts/legacy-deployer/
          mv artifacts/legacy-deployer/legacy-digirs/legacy-digirs.zip artifacts/legacy-deployer/
          mv artifacts/legacy-deployer/legacy-advertised-programs/legacy-advertised-programs.zip artifacts/legacy-deployer/
          mv artifacts/legacy-deployer/legacy-advertised-program-delivery/legacy-advertised-program-delivery.zip artifacts/legacy-deployer/

      - name: Backup and upload packages to S3
        run: |
          # Define package names
          declare -A packages=(
            ["legacy-common-libraries.zip"]="common_libs"
            ["legacy-authoring.zip"]="NewSystem"
            ["legacy-residuals-entry.zip"]="ResidualsEntry"
            ["legacy-auditing.zip"]="Auditing"
            ["legacy-digirs.zip"]="DigIRS"
            ["legacy-advertised-programs.zip"]="AdvertisedPrograms"
            ["legacy-advertised-program-delivery.zip"]="AdvPgmDelivery"
          )

          # Process each package
          for package_file in "${!packages[@]}"; do
            echo "Processing $package_file"

            # Determine S3 object keys
            if [ -z "$BRANCH_NAME" ]; then
              object_key="$package_file"
              backup_key="backup/$package_file"
            else
              object_key="$BRANCH_NAME/$package_file"
              backup_key="$BRANCH_NAME/backup/$package_file"
            fi

            # Backup existing package if it exists
            echo "Backing up existing package to $backup_key"
            aws s3 cp "s3://${{ steps.s3-bucket.outputs.bucket-name }}/$object_key" "s3://${{ steps.s3-bucket.outputs.bucket-name }}/$backup_key" || true

            # Upload new package
            echo "Uploading new package to $object_key"
            aws s3 cp "artifacts/legacy-deployer/$package_file" "s3://${{ steps.s3-bucket.outputs.bucket-name }}/$object_key"
          done
