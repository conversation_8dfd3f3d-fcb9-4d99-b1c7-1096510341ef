name: Deploy Processing App Image

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'non-prod'
        type: choice
        options:
          - non-prod
          - production
      region:
        description: 'Primary AWS region'
        required: true
        default: 'us-east-1'
        type: choice
        options:
          - us-east-1
          - us-west-2
      account:
        description: 'AWS account'
        required: true
        default: 'awsaaianp'
        type: string
      repository-name:
        description: 'ECR repository name'
        required: true
        default: 'processingapps'
        type: string
      run-security-scan:
        description: 'Run security scan'
        required: false
        default: false
        type: boolean

jobs:
  # Security scanning job (optional)
  security-scan:
    if: github.event.inputs.run-security-scan == 'true'
    uses: ./.github/workflows/reusable-security-scan.yml
    with:
      scan-type: 'all'
      upload-sarif: true
      fail-on-severity: 'high'

  # AWS authentication and setup
  aws-auth:
    uses: ./.github/workflows/reusable-aws-auth.yml
    with:
      aws-region: ${{ github.event.inputs.region }}
      environment: ${{ github.event.inputs.environment }}
      account: ${{ github.event.inputs.account }}
      setup-docker: true

  # Package the codebase
  package-codebase:
    name: Package Codebase
    needs: aws-auth
    uses: ./.github/workflows/reusable-build-steps.yml
    with:
      operation: 'zip'
      output-path: 'artifacts/codebasePackage.zip'
      exclude-patterns: '.git/*,.github/*,artifacts/*'
      artifact-name: 'codebase-package'

  # Build and push Docker image using reusable workflow
  build-push-image:
    name: Build and Push Processing App Image
    needs: [aws-auth, package-codebase]
    uses: ./.github/workflows/reusable-docker-build-push.yml
    with:
      environment: ${{ github.event.inputs.environment }}
      region: ${{ github.event.inputs.region }}
      account: ${{ github.event.inputs.account }}
      repository-name: ${{ github.event.inputs.repository-name }}
      docker-context-path: 'infrastructure/docker/repos/processing_task'
      dockerfile-path: 'infrastructure/docker/repos/processing_task/Dockerfile'
      codebase-artifact-name: 'codebase-package'
      run-security-scan: ${{ github.event.inputs.run-security-scan == 'true' }}
      enable-multi-region: true
