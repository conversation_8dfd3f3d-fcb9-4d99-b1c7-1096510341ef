name: Reusable Docker Build and Push

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: 'Environment (non-prod or production)'
      region:
        required: true
        type: string
        description: 'AWS region'
      account:
        required: true
        type: string
        description: 'AWS account'
      repository-name:
        required: true
        type: string
        description: 'ECR repository name'
      docker-context-path:
        required: true
        type: string
        description: 'Path to Docker context (relative to repo root)'
      dockerfile-path:
        required: true
        type: string
        description: 'Path to Dockerfile (relative to repo root)'
      codebase-artifact-name:
        required: false
        type: string
        default: 'codebase-package'
        description: 'Name of the codebase artifact'
      image-tag:
        required: false
        type: string
        default: 'latest'
        description: 'Docker image tag'
      run-security-scan:
        required: false
        type: boolean
        default: false
        description: 'Whether to run security scan on the image'
      enable-multi-region:
        required: false
        type: boolean
        default: true
        description: 'Whether to push to multiple regions for production'
    outputs:
      image-uri:
        description: 'URI of the built Docker image'
        value: ${{ jobs.build-push.outputs.image-uri }}
      additional-regions:
        description: 'Additional regions where image was pushed'
        value: ${{ jobs.build-push.outputs.additional-regions }}

jobs:
  build-push:
    name: <PERSON><PERSON> and Push Docker Image
    runs-on: ubuntu-latest
    outputs:
      image-uri: ${{ steps.build-image.outputs.image-uri }}
      additional-regions: ${{ steps.multi-region.outputs.additional-regions }}
    
    steps:
      - name: Validate inputs
        uses: ./.github/actions/validate-inputs
        with:
          environment: ${{ inputs.environment }}
          region: ${{ inputs.region }}
          account: ${{ inputs.account }}

      - name: Setup AWS and Docker
        id: aws-setup
        uses: ./.github/actions/setup-aws-docker
        with:
          aws-account: ${{ inputs.account }}
          aws-region: ${{ inputs.region }}

      - name: Prepare Docker context
        uses: ./.github/actions/prepare-docker-context
        with:
          artifact-name: ${{ inputs.codebase-artifact-name }}
          docker-context-path: ${{ inputs.docker-context-path }}

      - name: Download Docker context
        uses: actions/download-artifact@v3
        with:
          name: docker-context
          path: ${{ inputs.docker-context-path }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push Docker image
        id: build-image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ${{ inputs.dockerfile-path }}
          push: true
          tags: |
            ${{ steps.aws-setup.outputs.registry }}/${{ inputs.repository-name }}_${{ inputs.environment }}:${{ inputs.image-tag }}
          build-args: |
            ENVIRONMENT=${{ inputs.environment }}

      - name: Set image URI output
        id: set-image-uri
        run: |
          IMAGE_URI="${{ steps.aws-setup.outputs.registry }}/${{ inputs.repository-name }}_${{ inputs.environment }}:${{ inputs.image-tag }}"
          echo "image-uri=$IMAGE_URI" >> $GITHUB_OUTPUT

      - name: Push to additional regions
        id: multi-region
        if: inputs.enable-multi-region
        uses: ./.github/actions/multi-region-docker-push
        with:
          primary-region: ${{ inputs.region }}
          environment: ${{ inputs.environment }}
          repository-name: ${{ inputs.repository-name }}
          aws-account-id: ${{ steps.aws-setup.outputs.aws-account-id }}
          primary-registry: ${{ steps.aws-setup.outputs.registry }}
          image-tag: ${{ inputs.image-tag }}

      - name: Run security scan
        if: inputs.run-security-scan
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ steps.set-image-uri.outputs.image-uri }}
          format: 'table'
          exit-code: '0'
          severity: 'CRITICAL,HIGH'
